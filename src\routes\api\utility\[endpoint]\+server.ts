import type { <PERSON>quest<PERSON><PERSON><PERSON> } from './$types';

import { effectfulFetch } from '$lib/utils/fetch';
import { Effect } from 'effect';
import { json } from '@sveltejs/kit';

export const GET: RequestHandler = async ({ url, params }) => {
	const keyword = url.searchParams.get('keyword') ?? '';
	const limit = url.searchParams.get('limit') ?? '10';
	const offset = url.searchParams.get('offset') ?? '0';

	const getUtility = effectfulFetch(
		`/${params.endpoint}?keyword=${keyword}&limit=${limit}&offset=${offset}`
	);
	const data = await Effect.runPromise(getUtility);

	return json(data);
};
