<script lang="ts">
	import { getOrder } from '$lib/remote/order.remote';
	import type { OrderStatus } from '$lib/schema/literal';
	import OrderCard from './OrderCard.svelte';

	interface IProps {
		kind: OrderStatus;
	}

	const { kind }: IProps = $props();

	const orders = $derived(await getOrder(kind));
</script>

<svelte:boundary>
	<div
		class="rounded p-4 pt-0 {kind === 'Antrian'
			? 'bg-blue-100'
			: kind === 'Dikerjakan'
				? 'bg-amber-100'
				: kind === 'Selesai'
					? 'bg-emerald-100'
					: 'bg-gray-100'}"
	>
		<h2
			class="mb-4 border-b border-b-white py-4 text-center leading-2 font-bold tracking-wide uppercase"
		>
			{kind}
		</h2>

		<div class="flex flex-col gap-4">
			{#if $effect.pending()}
				<span class="loading loading-ball loading-sm"></span>
			{/if}

			{#each orders ?? [] as order (order.nomor_order)}
				<OrderCard {order} />
			{:else}
				<p class="text-xs font-extralight tracking-wide p-4 text-center">
					tidak ada order dalam kategori ini.
				</p>
			{/each}
		</div>
	</div>
</svelte:boundary>
