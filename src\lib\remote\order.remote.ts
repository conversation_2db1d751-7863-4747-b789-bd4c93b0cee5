import type { Order } from '$lib/schema/order';

import { form, query } from '$app/server';
import { Effect, Schema } from 'effect';

import { effectfulFetch } from '$lib/utils/fetch';
import { orderStatus } from '$lib/schema/literal';

const OrderTypeSchema = Schema.Literal(...orderStatus).pipe(Schema.standardSchemaV1);
export const getOrder = query(OrderTypeSchema, async (orderStatus) => {
	const getOrder = effectfulFetch<Order[]>(`/order/status/${orderStatus}`);
	const response = await Effect.runPromise(getOrder);

	if (response.kind === 'success') return response.data;
	else return [];
});

export const assignOrder = form(async (data) => {
	const order: Order = JSON.parse(data.get('order') as string);

	const action = effectfulFetch(`/order`, {
		method: 'PUT',
		body: JSON.stringify({
			...order,
			status_order: '<PERSON><PERSON>jakan',
			id_bengkel: order.bengkel.id_bengkel,
			id_karyawan: order.karyawan.id_karyawan,
			id_customer: order.customer.id_customer,
			nomor_polisi: order.kendaraan.nomor_polisi
		})
	});

	const response = await Effect.runPromise(action);

	return response;
});

export const deleteOrder = form(async (data) => {
	const nomor_order = data.get('nomor_order') as string;

	const action = effectfulFetch(`/order/${nomor_order}`, {
		method: 'DELETE'
	});

	const response = await Effect.runPromise(action);

	return response;
});

export const voidInvoice = form(async (data) => {
	const nomor_invoice = data.get('id') as string;

	const action = effectfulFetch(`/invoice`, {
		method: 'DELETE',
		body: JSON.stringify({ nomor_invoice, keterangan_void: '' })
	});

	const response = await Effect.runPromise(action);
	return response;
});
