import type { Sparepart } from '$lib/schema/general';
import { effectfulFetch } from '$lib/utils/fetch';
import { Effect } from 'effect';
import type { PageServerLoad } from './$types';

export const load: PageServerLoad = async ({ params }) => {
	const opnameId = params.id;

	const getSparepart = effectfulFetch<Sparepart[]>('/sparepart');
	const response = await Effect.runPromise(getSparepart);

	return { opnameId, list: response.data };
};
