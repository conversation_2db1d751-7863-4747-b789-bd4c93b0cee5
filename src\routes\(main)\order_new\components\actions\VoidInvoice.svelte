<script lang="ts">
	import type { Invoice } from '$lib/schema/invoice';
	import { voidInvoice, getOrder } from '$lib/remote/order.remote';

	import Icon from '@iconify/svelte';

	import { getConfirmState } from '$lib/utils/confirm/ConfirmState.svelte';
	import { getToastState } from '$lib/utils/toast/ToastState.svelte';
	import ConfirmLoader from '$lib/utils/confirm/ConfirmLoader.svelte';

	const confirmState = getConfirmState();
	const toastState = getToastState();

	interface IProps {
		order: Order;
	}
	const { order }: IProps = $props();

	const confirmation = (e: Event) => {
		confirmState.asking(
			{
				title: 'Konfirma<PERSON>',
				description: '<PERSON><PERSON><PERSON>h Anda yakin akan membatalkan (void) invoice ini?',
				loader: 'delete:invoice:' + invoice.nomor_invoice
			},
			() => confirmState.submitForm(e)
		);
	};
</script>

<form
	{...deleteinvoice.enhance(async ({ submit, form }) => {
		form.append('nomor_invoice', invoice.nomor_invoice ?? '');
		await submit().updates(
			getinvoice(invoice.status).withOverride(
				(invoices) => invoices?.filter((o) => o.nomor_invoice !== invoice.nomor_invoice) ?? []
			)
		);
		if (deleteinvoice.result?.kind === 'success')
			toastState.add({
				message: 'invoice berhasil dihapus',
				type: 'success'
			});

		confirmState.loader = '';
	})}
	class="flex flex-col items-center justify-center p-0"
>
	<button class="btn btn-ghost btn-error w-full" type="button" onclick={(e) => confirmation(e)}>
		<ConfirmLoader name="delete:invoice:{invoice.nomor_invoice}">
			<Icon icon="mdi:trash-can-outline" /> Hapus
		</ConfirmLoader>
	</button>
</form>
