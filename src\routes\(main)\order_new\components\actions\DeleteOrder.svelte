<script lang="ts">
	import type { Order } from '$lib/schema/order';
	import { deleteOrder, getOrder } from '$lib/remote/order.remote';

	import Icon from '@iconify/svelte';

	import { getConfirmState } from '$lib/utils/confirm/ConfirmState.svelte';
	import { getToastState } from '$lib/utils/toast/ToastState.svelte';
	import ConfirmLoader from '$lib/utils/confirm/ConfirmLoader.svelte';

	const confirmState = getConfirmState();
	const toastState = getToastState();

	interface IProps {
		order: Order;
	}
	const { order }: IProps = $props();

	const confirmation = (e: Event) => {
		confirmState.asking(
			{
				title: '<PERSON>nfirma<PERSON>',
				description: 'Apakah Anda yakin akan menghapus order ini?',
				loader: 'delete:order:' + order.nomor_order
			},
			() => confirmState.submitForm(e)
		);
	};
</script>

<form
	{...deleteOrder.enhance(async ({ submit, form }) => {
		form.append('nomor_order', order.nomor_order ?? '');
		await submit().updates(
			getOrder(order.status).withOverride(
				(orders) => orders?.filter((o) => o.nomor_order !== order.nomor_order) ?? []
			)
		);
		if (deleteOrder.result?.kind === 'success')
			toastState.add({
				message: 'Order berhasil dihapus',
				type: 'success'
			});

		confirmState.loader = '';
	})}
	class="flex flex-col items-center justify-center p-0"
>
	<button class="btn btn-ghost btn-error w-full" type="button" onclick={(e) => confirmation(e)}>
		<ConfirmLoader name="delete:order:{order.nomor_order}">
			<Icon icon="mdi:trash-can-outline" /> Hapus
		</ConfirmLoader>
	</button>
</form>
