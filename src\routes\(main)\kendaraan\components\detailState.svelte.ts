import { type Kendaraan, _Kendaraan } from '$lib/schema/general';
import { type Mutable } from 'effect/Types';
import { getContext, setContext } from 'svelte';

export interface DetailState {
	modal: HTMLDialogElement | undefined;
	body: Mutable<Kendaraan>;
	mode: 'add' | 'view' | 'edit';

	addNewBody(): void;
}

export default class DetailStateClass implements DetailState {
	modal = $state<HTMLDialogElement>();
	body = $state<Mutable<Kendaraan>>(_Kendaraan);
	mode = $state<'add' | 'view' | 'edit'>('add');

	addNewBody() {
		this.body = _Kendaraan;
	}
}

const DETAIL_STATE_KEY = Symbol('@@detail-state@@');

export function setDetailState(): void {
	setContext(DETAIL_STATE_KEY, new DetailStateClass());
}

export function getDetailState(): DetailState {
	return getContext<DetailState>(DETAIL_STATE_KEY);
}
