import { env } from '$env/dynamic/private';
import { Effect, pipe } from 'effect';

import { BackendError, FetchError, JSONError } from '$lib/errors';
import { fail, type ActionFailure } from '@sveltejs/kit';
import { getRequestEvent } from '$app/server';
import type { JSONResponse } from './types';
import HttpStatusCode from './http_status_code';

export const retrieve = <T>(
	fetch: (input: RequestInfo | URL, init?: RequestInit) => Promise<Response>,
	url: string,
	init: RequestInit = {}
) =>
	pipe(
		Effect.tryPromise({
			try: () => fetch(env.API_HOST + ':' + env.API_PORT + url, init),
			catch: () => {
				return new FetchError();
			}
		}),
		Effect.flatMap((response) => {
			return Effect.tryPromise({
				try: () => response.json(),
				catch: () => {
					return new JSONError();
				}
			});
		}),
		Effect.flatMap((json) => {
			if (json.status < 200 || json.status >= 300) {
				return Effect.fail(new Error(url + ` : ` + json.message));
			} else {
				const data = ('items' in json.data ? json.data.items : json.data) ?? [];
				const total_rows = ('items' in json.data ? json.data.total_rows : json.data.length) ?? 0;

				return Effect.succeed<{ data: T; total_rows: number }>({ data, total_rows });
			}
		})
	);

export const effectfulFetch = <T>(url: string, init: RequestInit = {}) =>
	pipe(
		Effect.tryPromise({
			try: () => {
				const { fetch } = getRequestEvent();
				return fetch(env.API_HOST + ':' + env.API_PORT + url, init);
			},
			catch: () => new FetchError()
		}),
		Effect.flatMap((response) => {
			return Effect.tryPromise({
				try: () => response.json(),
				catch: () => new JSONError()
			});
		}),
		Effect.flatMap((json: JSONResponse<T>) => {
			if (json.status === HttpStatusCode.INTERNAL_SERVER_ERROR_500)
				return Effect.fail(new BackendError(json.status, json.message, [json.message]));
			else {
				let data: T | null = json.data?.items ?? null;
				let total_rows: number = json.data?.total_rows ?? 0;

				return Effect.succeed({ data, total_rows, kind: 'success' as const });
			}
		}),
		Effect.catchTags({
			FetchError: () =>
				Effect.succeed({
					status: HttpStatusCode.INTERNAL_SERVER_ERROR_500,
					message: `${url} : Failed to fetch data`,
					kind: 'fetch' as const
				}),
			JSONError: () =>
				Effect.succeed({
					status: HttpStatusCode.INTERNAL_SERVER_ERROR_500,
					message: `${url} : Failed to parse JSON`,
					kind: 'json' as const
				}),
			BackendError: (_BackendError) =>
				Effect.succeed({
					status: _BackendError.status,
					message: `${url} : ${_BackendError.message}`,
					errors: _BackendError.errors,
					kind: 'backend' as const
				})
		})
	);

export const launch = (
	fetch: (input: RequestInfo | URL, init?: RequestInit) => Promise<Response>,
	url: string,
	init: RequestInit = {}
) =>
	pipe(
		Effect.tryPromise({
			try: () => {
				return fetch(env.API_HOST + ':' + env.API_PORT + url, init);
			},
			catch: () => new FetchError()
		}),
		Effect.flatMap((response) =>
			Effect.tryPromise({
				try: () => response.json(),
				catch: () => new JSONError()
			})
		),
		Effect.catchTags({
			FetchError: () => Effect.succeed(fail(500, { message: `${url} : Failed to fetch data` })),
			JSONError: () => Effect.succeed(fail(500, { message: `${url} : Failed to parse JSON` }))
		}),
		Effect.flatMap((json) => {
			return 'status' in json === false || json.status < 200 || json.status >= 300
				? Effect.fail(new BackendError(json.status ?? 500, json.message, json.errors))
				: Effect.succeed({ ...json });
		}),
		Effect.catchTags({
			BackendError: (_BackendError) =>
				Effect.succeed(
					fail(_BackendError.status, {
						message: `${url} : ${_BackendError.message}`,
						errors: _BackendError.errors
					})
				)
		})
	);

export const launch_fetch = (
	url: string,
	init: RequestInit = {}
): Effect.Effect<
	ActionFailure<
		{ message: string; errors: string[] } | { message: string; status: number; data: any }
	>,
	FetchError | JSONError | BackendError
> =>
	pipe(
		Effect.tryPromise({
			try: () => {
				const { fetch } = getRequestEvent();
				return fetch(env.API_HOST + ':' + env.API_PORT + url, init);
			},
			catch: () => new FetchError()
		}),
		Effect.flatMap((response) =>
			Effect.tryPromise({
				try: () => response.json(),
				catch: () => new JSONError()
			})
		),
		Effect.catchTags({
			FetchError: () => Effect.succeed(fail(500, { message: `${url} : Failed to fetch data` })),
			JSONError: () => Effect.succeed(fail(500, { message: `${url} : Failed to parse JSON` }))
		}),
		Effect.flatMap((json) => {
			return 'status' in json === false || json.status < 200 || json.status >= 300
				? Effect.fail(new BackendError(json.status ?? 500, json.message, json.errors))
				: Effect.succeed({ ...json });
		}),
		Effect.catchTags({
			BackendError: (_BackendError) =>
				Effect.succeed(
					fail(_BackendError.status, {
						message: `${url} : ${_BackendError.message}`,
						errors: _BackendError.errors
					})
				)
		})
	);
