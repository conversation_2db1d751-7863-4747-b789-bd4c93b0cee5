<script lang="ts">
	import type { Order } from '$lib/schema/order';

	import { calculateTimeDifference } from '$lib/scripts/dates';

	import Icon from '@iconify/svelte';
	import TimeElapsed from '../../order/list/[[status]]/components/TimeElapsed.svelte';
	import AssignOrder from './actions/AssignOrder.svelte';
	import { slide } from 'svelte/transition';
	import DeleteOrder from './actions/DeleteOrder.svelte';

	interface IProps {
		order: Order;
	}

	const { order }: IProps = $props();

	const startTime = order.status !== 'Dikerjakan' ? order.created_at : order.updated_at;
</script>

<div class="card bg-base-100 shadow" transition:slide>
	<div class="card-body gap-0 px-0 py-2 pb-0">
		<div class="card-title justify-center border-b pb-1">
			<div>{order.kendaraan.nomor_polisi}</div>
		</div>
		<div class="py-0.5 text-center text-xs leading-none font-light shadow">
			{order.nomor_order}
		</div>
		<div class="grid grid-cols-2">
			<div class="border-e p-2 text-center tracking-widest">
				{startTime?.split('T')[1].replace('Z', '')}
			</div>
			<div class="grid place-items-center p-2">
				{#if order.status !== 'Selesai' && order.status !== 'Void'}
					<TimeElapsed start={order.created_at} />
				{:else}
					<p class="text-xs">{calculateTimeDifference(order.created_at)}</p>
				{/if}
			</div>
		</div>

		<div class="card-actions absolute right-2">
			<button
				class="btn btn-ghost btn-circle btn-xs"
				style="anchor-name: --anchor-menu-{order.nomor_order};"
				popovertarget="popover-menu-{order.nomor_order}">&vellip;</button
			>
		</div>

		<ul
			class="dropdown menu rounded-box bg-base-100 w-52 shadow-sm"
			popover
			id="popover-menu-{order.nomor_order}"
			style="position-anchor:--anchor-menu-{order.nomor_order}"
		>
			<li><AssignOrder {order} /></li>
			<li><DeleteOrder {order} /></li>
		</ul>
	</div>
</div>
